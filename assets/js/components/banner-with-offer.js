/**
 * Banner with Offer Component - Optimized Performance
 * Handles countdown timer and offer banner interactions
 */

(function() {
    'use strict';

    // Performance optimized banner manager
    const BannerWithOffer = {

        // Cache for active timers
        activeTimers: new Map(),

        // Animation frame ID for cleanup
        animationFrameId: null,

        init() {
            // Use intersection observer for better performance
            this.setupIntersectionObserver();

            // Initialize all banner components
            this.initializeBanners();

            // Setup reduced motion detection
            this.setupReducedMotionDetection();
        },

        setupIntersectionObserver() {
            if (!window.IntersectionObserver) return;

            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const bannerId = entry.target.id.replace('offer-component-', '');

                    if (entry.isIntersecting) {
                        this.startTimer(bannerId);
                    } else {
                        this.pauseTimer(bannerId);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
        },

        initializeBanners() {
            // Find all banner components
            const banners = document.querySelectorAll('[id^="offer-component-"]');

            banners.forEach(banner => {
                const bannerId = banner.id.replace('offer-component-', '');
                const config = window.bannerOfferConfig?.[bannerId];

                if (!config) {
                    console.warn(`No configuration found for banner: ${bannerId}`);
                    return;
                }

                // Setup banner
                this.setupBanner(bannerId, config, banner);

                // Observe banner for intersection
                if (this.observer) {
                    this.observer.observe(banner);
                }

                // Apply animation class if enabled
                const offerContainer = banner.querySelector('.offer-container');
                if (offerContainer && config.offer_animation && config.offer_animation !== 'none') {
                    // Remove any existing animation classes
                    offerContainer.classList.remove('fadeIn', 'slideUp', 'scaleIn', 'none');
                    // Add the selected animation class
                    offerContainer.classList.add(config.offer_animation);
                }

                // Apply dynamic CSS variables
                if (config.offer_bg_color) {
                    banner.style.setProperty('--offer-bg-color', config.offer_bg_color);
                }

                if (config.timer_text_color) {
                    banner.style.setProperty('--timer-text-color', config.timer_text_color);
                }

                // Handle offer button visibility and text
                const offerButton = banner.querySelector('.offer-cta-button');
                if (offerButton) {
                    if (config.offer_button_enabled === false) {
                        offerButton.style.display = 'none';
                    } else {
                        offerButton.style.display = '';
                        if (config.button_text) {
                            offerButton.querySelector('.button-text').textContent = config.button_text;
                        }
                        // Update href if URL is provided
                        if (config.offer_url && config.offer_url !== '#') {
                            offerButton.href = config.offer_url;
                        }
                    }
                }

                // Handle timer visibility and styling
                const timerSection = banner.querySelector('.countdown-timer');
                const timerContainer = banner.querySelector('.offer-content h3');
                if (timerSection) {
                    if (config.timer_enabled === false) {
                        timerSection.style.display = 'none';
                        if (timerContainer) timerContainer.style.display = 'none';
                    } else {
                        timerSection.style.display = '';
                        if (timerContainer) timerContainer.style.display = '';
                        if (config.timer_text_color) {
                            timerSection.style.color = config.timer_text_color;
                        }
                    }
                }
            });
        },

        setupBanner(bannerId, config, bannerElement) {
            // Cache DOM elements
            const elements = {
                days: document.getElementById(`days-${bannerId}`),
                hours: document.getElementById(`hours-${bannerId}`),
                minutes: document.getElementById(`minutes-${bannerId}`),
                seconds: document.getElementById(`seconds-${bannerId}`),
                badge: document.getElementById(`offer-ended-badge-${bannerId}`),
                countdown: document.getElementById(`countdown-timer-${bannerId}`),
                banner: bannerElement
            };

            // Validate required elements
            if (!elements.days || !elements.hours || !elements.minutes || !elements.seconds) {
                // Only warn if timer is enabled
                if (config.timer_enabled !== false) {
                    console.error(`Missing timer elements for banner: ${bannerId}`);
                }
                return;
            }

            // Calculate end date
            const endDate = new Date();
            endDate.setDate(endDate.getDate() + (config.days || 0));

            // Store timer data
            this.activeTimers.set(bannerId, {
                elements,
                config,
                endDate,
                isActive: false,
                previousValues: { days: null, hours: null, minutes: null, seconds: null },
                lastUpdateTime: 0
            });

            // Apply dynamic styles
            this.applyDynamicStyles(bannerElement, config);
        },

        applyDynamicStyles(bannerElement, config) {
            // Create dynamic CSS for this banner
            const style = document.createElement('style');
            const bannerId = bannerElement.id;

            // Get timer background color from config
            const timerBgColor = config.bg_color || '#1DE9B6';
            const timerTextColor = config.timer_text_color || '#ffffff';
            const offerBgColor = config.offer_bg_color || '#f5f5f5';

            style.textContent = `
                #${bannerId} {
                    --gaming-primary: ${timerBgColor};
                    --gaming-primary-alpha-10: ${timerBgColor}1a;
                    --gaming-primary-alpha-20: ${timerBgColor}33;
                    --gaming-primary-alpha-30: ${timerBgColor}4d;
                    --gaming-primary-alpha-40: ${timerBgColor}66;
                    --gaming-primary-alpha-60: ${timerBgColor}99;
                    --gaming-primary-alpha-80: ${timerBgColor}cc;
                    --gaming-primary-alpha-90: ${timerBgColor}e6;
                    --offer-bg-color: ${offerBgColor};
                    --timer-text-color: ${timerTextColor};
                }
                #${bannerId} .countdown-timer {
                    color: ${timerTextColor} !important;
                }
                #${bannerId} .countdown-timer .countdown-box {
                    background: linear-gradient(135deg, ${timerBgColor}, ${timerBgColor}cc);
                    border: 1px solid ${timerBgColor};
                }
                #${bannerId} .offer-container {
                    background-color: ${offerBgColor} !important;
                }
                #${bannerId} .overlay {
                    opacity: ${config.overlay_opacity || 0.7};
                }
            `;

            document.head.appendChild(style);
        },

        startTimer(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData || timerData.isActive) return;

            // Only start timer if enabled
            if (timerData.config.timer_enabled === false) return;

            timerData.isActive = true;
            this.updateTimer(bannerId);
        },

        pauseTimer(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData) return;

            timerData.isActive = false;
        },

        updateTimer(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData || !timerData.isActive) return;

            const now = Date.now();
            const elapsed = now - timerData.lastUpdateTime;

            // Update only every second for performance
            if (elapsed >= 1000) {
                timerData.lastUpdateTime = now;

                const distance = timerData.endDate - now;

                if (distance < 0) {
                    this.handleCountdownEnd(bannerId);
                    return;
                }

                // Calculate time values
                const timeValues = {
                    days: Math.floor(distance / (1000 * 60 * 60 * 24)),
                    hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
                    minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
                    seconds: Math.floor((distance % (1000 * 60)) / 1000)
                };

                // Update only changed values
                this.updateTimeValues(bannerId, timeValues);
            }

            // Continue animation loop
            requestAnimationFrame(() => this.updateTimer(bannerId));
        },

        updateTimeValues(bannerId, timeValues) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData) return;

            Object.keys(timeValues).forEach(key => {
                if (timerData.previousValues[key] !== timeValues[key]) {
                    timerData.elements[key].textContent = timeValues[key];
                    timerData.previousValues[key] = timeValues[key];

                    // Animate value change
                    this.animateValueChange(timerData.elements[key], timerData.config);
                }
            });
        },

        animateValueChange(element, config) {
            // Check for reduced motion preference
            if (this.prefersReducedMotion()) return;

            // Use Web Animation API for better performance
            element.animate([
                {
                    transform: 'scale(1)',
                    textShadow: `0 0 10px ${config.bg_color || config.bgColor}, 0 0 5px ${config.bg_color || config.bgColor}`
                },
                {
                    transform: 'scale(1.1)',
                    textShadow: `0 0 20px ${config.bg_color || config.bgColor}, 0 0 10px ${config.bg_color || config.bgColor}`
                },
                {
                    transform: 'scale(1)',
                    textShadow: `0 0 10px ${config.bg_color || config.bgColor}, 0 0 5px ${config.bg_color || config.bgColor}`
                }
            ], {
                duration: 300,
                easing: 'cubic-bezier(0.2, 0, 0.2, 1)'
            });
        },

        handleCountdownEnd(bannerId) {
            const timerData = this.activeTimers.get(bannerId);
            if (!timerData) return;

            // Stop timer
            timerData.isActive = false;

            // Set all values to zero
            Object.keys(timerData.elements).forEach(key => {
                if (key !== 'badge' && key !== 'countdown' && key !== 'banner') {
                    timerData.elements[key].textContent = "0";
                }
            });

            // Show ended badge
            this.showEndedBadge(timerData.elements.badge, timerData.config);

            // Dispatch custom event
            timerData.elements.banner.dispatchEvent(new CustomEvent('offerEnded', {
                detail: { bannerId, timestamp: Date.now() }
            }));
        },

        showEndedBadge(badgeElement, config) {
            if (!badgeElement) return;

            badgeElement.style.display = 'flex';

            if (this.prefersReducedMotion()) return;

            // Animate badge appearance
            badgeElement.animate([
                {
                    transform: 'scale(0) rotate(0deg)',
                    opacity: 0,
                    boxShadow: `0 0 0 ${(config.bg_color || config.bgColor) || '#1DE9B6'}00`
                },
                {
                    transform: 'scale(1.2) rotate(15deg)',
                    opacity: 1,
                    boxShadow: `0 0 30px ${(config.bg_color || config.bgColor) || '#1DE9B6'}80`
                },
                {
                    transform: 'scale(1) rotate(15deg)',
                    opacity: 1,
                    boxShadow: `0 0 15px ${(config.bg_color || config.bgColor) || '#1DE9B6'}60`
                }
            ], {
                duration: 800,
                easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                fill: 'forwards'
            });
        },

        setupReducedMotionDetection() {
            // Check for reduced motion preference
            if (this.prefersReducedMotion()) {
                document.documentElement.classList.add('reduced-motion');
            }

            // Listen for changes
            if (window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                mediaQuery.addEventListener('change', () => {
                    if (mediaQuery.matches) {
                        document.documentElement.classList.add('reduced-motion');
                    } else {
                        document.documentElement.classList.remove('reduced-motion');
                    }
                });
            }
        },

        prefersReducedMotion() {
            return window.matchMedia &&
                   window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        },

        // Cleanup method
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
            }

            this.activeTimers.clear();

            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            BannerWithOffer.init();
        }, { once: true });
    } else {
        BannerWithOffer.init();
    }

    // Expose to global scope for external control
    window.BannerWithOffer = BannerWithOffer;

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        BannerWithOffer.destroy();
    });

})();

// Performance monitoring (optional)
if (window.performance && window.performance.mark) {
    window.performance.mark('banner-with-offer-script-loaded');
}
